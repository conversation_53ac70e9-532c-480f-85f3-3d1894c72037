import { useTemplateHookReturnType } from '@/app/(dashboard)/admin/template/AddTemplate';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { PDFGenerator } from '@/components/elements/pdf/PDF-Generator';
import SearchContact from '@/components/elements/search/SearchContact';
import Status from '@/components/elements/status/Status';
import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { Box, Stack, Text } from '@chakra-ui/react';
import { PDFViewer } from '@react-pdf/renderer';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import NoClientPackageModal from '../NoClientPackageModal';
import CreateSoapNote from './CreateSoapNote';
import InvoiceCreation from './InvoiceCreation';
import Payment from './Payment';
import SendInvoice from './SendInvoice';

export default function SoapNoteFlow({
  soapNoteHook,
  section,
  // abbr,
  templateHook,
  initialBooking,
  isFetching,
}: {
  soapNoteHook: TCreateInvoiceHook;
  section: 'slp' | 'client';
  abbr?: any;
  templateHook: useTemplateHookReturnType;
  initialBooking: any;
  isFetching: boolean;
}) {
  const { booking } = soapNoteHook;
  const isClient = section === 'client';
  // const amountDue =
  //   Number(initialBooking?.slp_notes?.invoice?.total_price) -
  //   Number(initialTransaction?.amount || 0);

  const totalPaid = initialBooking?.slp_notes?.invoice?.transactions?.reduce(
    (prev: number, currentTransaction: any) => {
      return prev + Number(currentTransaction?.amount || 0);
    },
    0
  );
  const totalPrice = Number(initialBooking?.slp_notes?.invoice?.total_price);
  const amountDue = totalPaid > totalPrice ? 0 : totalPrice - Number(totalPaid);
  // console.log('invoice', initialBooking?.slp_notes?.invoice);
  const router = useRouter();
  // const determineStatus = () => {
  //   if (amountDue == 0 && transactions.length) {
  //     return 'Paid';
  //   }
  //   if (amountDue > 0 && transactions.length) {
  //     return 'Partially paid';
  //   }
  //   if (amountDue > 0 && !transactions.length) {
  //     return 'Pendinng';
  //   }
  // };
  // console.log('transactions?.transaction_date', transactions);
  // console.log('date?.transaction_date', initialBooking);
  return (
    <Box pl={'1rem'}>
      {(booking && booking?.clients) || isClient ? (
        <Box maxW={'full'} w={'full'}>
          <NoClientPackageModal
            isOpenClientNoPackage={soapNoteHook?.isOpenClientNoPackage}
            onNoClientPackageClose={soapNoteHook?.handleCloseNoPackageModal}
          />
          <Box display={'flex'} justifyContent={'space-between'} mb={'1rem'}>
            <Box display={'flex'} gap={'1.5rem'}>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Status
                </Text>
                {/* <Box
                  px={'1rem'}
                  color={'gray.400'}
                  borderRadius={'sm'}
                  bg={'green.300'}
                  textAlign="center"
                > */}
                <Status
                  name={initialBooking?.slp_notes?.invoice?.status ?? 'pending'}
                  // name={determineStatus()}
                />
                {/* </Box> */}
              </Box>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Customer
                </Text>
                <Text
                  onClick={() => {
                    router.replace(
                      `/contacts/${booking?.clients?.id}?tab=profile`
                    );
                  }}
                  cursor={'pointer'}
                  color={'#E97A5B'}
                  fontWeight={'bold'}
                >
                  {booking?.clients?.first_name} {booking?.clients?.last_name}
                </Text>
              </Box>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Date
                </Text>
                <Text>
                  {moment(initialBooking?.appointment?.split('T')[0])?.format(
                    ' MMMM D, YYYY'
                  )}
                </Text>
              </Box>
            </Box>
            <Box display={'flex'} gap={'1.5rem'}>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Amount due
                </Text>
                <Text>{formatMoney(amountDue)}</Text>
              </Box>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Due On
                </Text>
                <Text>
                  {moment(
                    initialBooking?.slp_notes?.invoice?.due_date?.split(
                      'T'
                    )[0] || initialBooking?.appointment.split('T')[0]
                  ).format('MMMM D, YYYY')}
                </Text>
              </Box>
            </Box>
          </Box>

          {/* <Text fontWeight={'semibold'} fontSize={'1.5rem'}>
            {booking?.clients?.first_name} {booking?.clients?.last_name}
          </Text>
          <Box position={'relative'}>
            <Text fontWeight={600} mb={'2px'}>
              Session Details
            </Text>
          </Box> */}
          <CreateSoapNote
            soapNoteHook={soapNoteHook}
            templateHook={templateHook}
            booking={initialBooking}
          />
          <Box
            minH={'2rem'}
            bg={'gray.100'}
            // h={'100%'}
            w={'3px'}
            ml={'1.5rem'}
          ></Box>
          <InvoiceCreation
            soapNoteHook={soapNoteHook}
            booking={initialBooking}
          />
          <Box
            minH={'2rem'}
            bg={'gray.100'}
            // h={'100%'}
            w={'3px'}
            ml={'1.5rem'}
          ></Box>
          <SendInvoice soapNoteHook={soapNoteHook} booking={initialBooking} />

          {initialBooking?.slp_notes?.invoice &&
            !(
              Number(initialBooking?.slp_notes?.invoice?.package_used?.length) >
              0
            ) && (
              <Box
                minH={'2rem'}
                bg={'gray.100'}
                // h={'100%'}
                w={'3px'}
                ml={'1.5rem'}
              ></Box>
            )}
          {initialBooking?.slp_notes?.invoice &&
            !(
              Number(initialBooking?.slp_notes?.invoice?.package_used?.length) >
              0
            ) && (
              <Payment
                soapNoteHook={soapNoteHook}
                initialBooking={initialBooking}
              />
            )}
          {isFetching ? (
            <AnimateLoader pt={'5rem'} />
          ) : !isFetching && initialBooking?.slp_notes?.invoice ? (
            <Box
              h={'fit-content'}
              mt={'2rem'}
              shadow={'md'}
              // border={'2px red solid'}
            >
              <PDFViewer
                style={{ height: '70vh', width: '100%' }}
                showToolbar={false}
              >
                <PDFGenerator
                  name={String(
                    initialBooking?.slp_notes?.invoice?.clients?.display_name ||
                      initialBooking?.slp_notes?.invoice?.name
                  )}
                  email={String(
                    initialBooking?.slp_notes?.invoice?.clients?.email ||
                      initialBooking?.slp_notes?.invoice?.email ||
                      initialBooking?.clients?.initial_email
                  )}
                  receiptNumber={String(
                    initialBooking?.slp_notes?.invoice?.invoice_number
                  )}
                  transactions={
                    initialBooking?.slp_notes?.invoice?.transactions ?? []
                  }
                  date={String(
                    moment(
                      initialBooking?.slp_notes?.invoice?.invoice_date?.split(
                        'T'
                      )[0]
                    ).format('MMMM D, YYYY')
                  )}
                  dueDate={String(
                    moment(
                      initialBooking?.slp_notes?.invoice?.due_date?.split(
                        'T'
                      )[0]
                    ).format('MMMM D, YYYY')
                  )}
                  amountDue={Number(amountDue || 0)}
                  activity={String(
                    initialBooking?.slp_notes?.invoice?.product || ''
                  )}
                  invoice={{
                    ...initialBooking?.slp_notes?.invoice,
                    services_purchases: initialBooking?.services_purchases,
                  }}
                  referral={initialBooking?.slp_notes?.invoice?.referral ?? ''}
                  quantity={Number(
                    initialBooking?.slp_notes?.invoice?.qty || 0
                  )}
                  rate={Number(
                    initialBooking?.slp_notes?.invoice?.total_price || 0
                  )}
                  balance={0}
                  memo={String(initialBooking?.slp_notes?.invoice?.memo || '')}
                />
              </PDFViewer>
            </Box>
          ) : null}
        </Box>
      ) : (
        // New Session Modal
        <Stack>
          <label className="font-medium text-gray-900">Lookup Client</label>
          <SearchContact
            setSearchResult={(e: any) => {
              soapNoteHook?.setSearchResult(e);
            }}
            searchResult={soapNoteHook?.searchResult}
            selectExistingUser={(item) =>
              soapNoteHook?.handleSearchSelect(item)
            }
          />
        </Stack>
      )}
    </Box>
  );
}
