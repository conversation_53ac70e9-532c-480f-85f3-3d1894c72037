'use server';
import { NextRequest, NextResponse } from 'next/server';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';

// Define a GET handler for fetching the client by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const supabase = createSupabaseServer();

  const { data, error } = await supabase
    .from(tableNames.bookings)
    .select(
      `*, 
      clients(*,  packages(*), invoices(*)), 
      slp_notes(*,invoice:invoices!slp_notes_invoice_id_fkey(*, slp_data:users(*),services(*), transactions:transactions_invoice_id_fkey(*))),
      services_purchases(*, invoice_items(*, invoice_id(*), services(*)))
      
      `
      // package_used:redeemed_sessions(*),
    )
    .eq('id', id);
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  const booking = { ...data[0] };
  const servicePurchases = booking?.services_purchases
    ?.map((item: any) => {
      if (item?.booking_id === booking?.id) {
        return {
          ...item,
          invoice_items: {
            ...item?.invoice_items,
            quantity: 1,
          },
        };
      }
    })
    .filter(Boolean);

  const totalPrice = servicePurchases?.reduce(
    (sum: number, item: any) => sum + item?.invoice_items?.price,
    0
  );
  const invoice = booking?.slp_notes?.invoice;
  invoice.total_price = totalPrice;

  return NextResponse.json([
    {
      ...booking,
      slp_notes: {
        ...booking?.slp_notes,
        invoice: {
          ...invoice,
          // transactions,
          // invoice_items: isRedeemed ? undefined : invoice?.invoice_items,
        },
      },
      services_purchases: servicePurchases,
    },
  ]);
}

// Define a PATCH handler for fetching the client by ID
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const supabase = await createSupabaseServer();

  const { payload } = await request.json();
  if (!id || !payload) {
    return NextResponse.json(
      { message: 'ID and data are required' },
      { status: 400 }
    );
  }
  console.log('payload is ', payload);

  const { data: updatedClient, error } = await supabase
    .from(tableNames.bookings)
    .update(payload)
    .eq('id', Number(id))
    .select();
  console.log('error', error);

  console.log('error is', error);
  if (error) {
    console.log('error', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  return NextResponse.json(updatedClient?.[0]);
}
